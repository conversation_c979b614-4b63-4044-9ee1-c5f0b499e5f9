"""
A.T.L.A.S. User Acceptance Testing (UAT) Framework
Comprehensive UAT scenarios for validating user-facing functionality
"""

import asyncio
import logging
import json
import time
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UATScenario:
    """User Acceptance Test scenario"""
    def __init__(self, name: str, description: str, category: str):
        self.name = name
        self.description = description
        self.category = category
        self.status = "NOT_RUN"
        self.start_time = None
        self.end_time = None
        self.duration = 0.0
        self.error = None
        self.results = {}
        self.user_feedback = ""
        
    def start(self):
        self.start_time = time.time()
        self.status = "RUNNING"
        
    def complete(self, success: bool = True, error: str = None, results: Dict = None, feedback: str = ""):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time if self.start_time else 0
        self.status = "PASSED" if success else "FAILED"
        self.error = error
        self.results = results or {}
        self.user_feedback = feedback


class AtlasUATFramework:
    """Comprehensive User Acceptance Testing framework for A.T.L.A.S."""
    
    def __init__(self):
        self.scenarios = []
        self.start_time = datetime.now()
        self.categories = {
            "trading": "Trading Functionality",
            "ai": "AI Conversation",
            "scanner": "Market Scanner",
            "risk": "Risk Management",
            "ui": "User Interface",
            "performance": "Performance & Responsiveness"
        }
        
    def create_scenario(self, name: str, description: str, category: str) -> UATScenario:
        """Create a new UAT scenario"""
        scenario = UATScenario(name, description, category)
        self.scenarios.append(scenario)
        return scenario
    
    async def uat_trading_paper_mode_verification(self) -> UATScenario:
        """UAT: Verify paper trading mode is enforced"""
        scenario = self.create_scenario(
            "Paper Trading Mode Verification",
            "Verify that all trading operations are in paper trading mode only",
            "trading"
        )
        scenario.start()
        
        try:
            from atlas_trading_core import AutoTradingEngine, TradingGodEngine
            
            # Test 1: Auto Trading Engine
            auto_engine = AutoTradingEngine()
            paper_mode_enforced = auto_engine.paper_trading_mode == True
            
            # Test 2: Trading God Engine
            god_engine = TradingGodEngine()
            analysis = await god_engine.generate_6_point_analysis("AAPL", "UAT test analysis")
            
            # Test 3: Verify no live trading capabilities
            live_trading_blocked = True
            try:
                auto_engine.set_paper_trading_mode(False)
                live_trading_blocked = False
            except:
                pass  # Expected to fail
            
            results = {
                "paper_mode_enforced": paper_mode_enforced,
                "analysis_generated": len(analysis) > 100 if isinstance(analysis, str) else False,
                "live_trading_blocked": live_trading_blocked,
                "trading_engines_tested": 2
            }
            
            success = paper_mode_enforced and live_trading_blocked
            feedback = "✅ Paper trading mode properly enforced" if success else "❌ Paper trading enforcement failed"
            
            scenario.complete(success, results=results, feedback=feedback)
            
        except Exception as e:
            scenario.complete(False, str(e))
        
        return scenario
    
    async def uat_ai_conversation_flow(self) -> UATScenario:
        """UAT: Test AI conversation capabilities"""
        scenario = self.create_scenario(
            "AI Conversation Flow",
            "Test AI conversation capabilities and response quality",
            "ai"
        )
        scenario.start()
        
        try:
            from atlas_ai_core import AtlasConversationalEngine
            
            ai_engine = AtlasConversationalEngine()
            
            # Test conversation scenarios
            test_messages = [
                "Hello, I'm new to trading",
                "What is the current price of AAPL?",
                "Should I buy TSLA stock?",
                "Explain the Lee Method",
                "What are the risks of options trading?"
            ]
            
            responses = []
            for i, message in enumerate(test_messages):
                try:
                    response = await ai_engine.process_message(message, f"uat_session_{i}")
                    responses.append({
                        "message": message,
                        "response_received": response is not None,
                        "response_length": len(str(response)) if response else 0
                    })
                except Exception as e:
                    responses.append({
                        "message": message,
                        "response_received": False,
                        "error": str(e)
                    })
            
            successful_responses = len([r for r in responses if r.get("response_received", False)])
            
            results = {
                "total_messages_tested": len(test_messages),
                "successful_responses": successful_responses,
                "success_rate": (successful_responses / len(test_messages)) * 100,
                "responses": responses
            }
            
            success = successful_responses >= len(test_messages) * 0.8  # 80% success rate
            feedback = f"✅ AI conversation working ({successful_responses}/{len(test_messages)} responses)" if success else f"❌ AI conversation issues ({successful_responses}/{len(test_messages)} responses)"
            
            scenario.complete(success, results=results, feedback=feedback)
            
        except Exception as e:
            scenario.complete(False, str(e))
        
        return scenario
    
    async def uat_market_scanner_functionality(self) -> UATScenario:
        """UAT: Test market scanner functionality"""
        scenario = self.create_scenario(
            "Market Scanner Functionality",
            "Test Lee Method scanner and market analysis capabilities",
            "scanner"
        )
        scenario.start()
        
        try:
            from atlas_lee_method import LeeMethodScanner
            
            scanner = LeeMethodScanner()
            
            # Test scanner initialization
            scanner_initialized = scanner is not None
            
            # Test with sample symbols
            test_symbols = ["AAPL", "MSFT", "GOOGL"]
            scan_results = []
            
            for symbol in test_symbols:
                try:
                    # Test individual symbol scanning
                    result = await scanner.scan_symbol(symbol)
                    scan_results.append({
                        "symbol": symbol,
                        "scan_successful": result is not None,
                        "has_signal": result.get("signal_detected", False) if result else False
                    })
                except Exception as e:
                    scan_results.append({
                        "symbol": symbol,
                        "scan_successful": False,
                        "error": str(e)
                    })
            
            successful_scans = len([r for r in scan_results if r.get("scan_successful", False)])
            
            results = {
                "scanner_initialized": scanner_initialized,
                "symbols_tested": len(test_symbols),
                "successful_scans": successful_scans,
                "scan_results": scan_results
            }
            
            success = scanner_initialized and successful_scans >= len(test_symbols) * 0.5  # 50% success rate
            feedback = f"✅ Scanner working ({successful_scans}/{len(test_symbols)} scans)" if success else f"❌ Scanner issues ({successful_scans}/{len(test_symbols)} scans)"
            
            scenario.complete(success, results=results, feedback=feedback)
            
        except Exception as e:
            scenario.complete(False, str(e))
        
        return scenario
    
    async def uat_risk_management_validation(self) -> UATScenario:
        """UAT: Test risk management functionality"""
        scenario = self.create_scenario(
            "Risk Management Validation",
            "Test risk assessment and position sizing functionality",
            "risk"
        )
        scenario.start()
        
        try:
            from atlas_risk_core import AtlasRiskEngine
            
            risk_engine = AtlasRiskEngine()
            risk_engine.portfolio_value = 100000  # $100k test portfolio
            
            # Test risk assessments
            test_positions = [
                {"symbol": "AAPL", "quantity": 100, "price": 150.0},
                {"symbol": "MSFT", "quantity": 50, "price": 300.0},
                {"symbol": "GOOGL", "quantity": 25, "price": 2500.0}
            ]
            
            assessments = []
            for pos in test_positions:
                try:
                    assessment = await risk_engine.assess_position_risk(
                        pos["symbol"], pos["quantity"], pos["price"], 100000
                    )
                    assessments.append({
                        "symbol": pos["symbol"],
                        "assessment_successful": assessment is not None,
                        "risk_score": assessment.risk_score if assessment else None,
                        "position_size": assessment.position_size if assessment else None
                    })
                except Exception as e:
                    assessments.append({
                        "symbol": pos["symbol"],
                        "assessment_successful": False,
                        "error": str(e)
                    })
            
            successful_assessments = len([a for a in assessments if a.get("assessment_successful", False)])
            
            results = {
                "positions_tested": len(test_positions),
                "successful_assessments": successful_assessments,
                "assessments": assessments
            }
            
            success = successful_assessments >= len(test_positions) * 0.8  # 80% success rate
            feedback = f"✅ Risk management working ({successful_assessments}/{len(test_positions)} assessments)" if success else f"❌ Risk management issues ({successful_assessments}/{len(test_positions)} assessments)"
            
            scenario.complete(success, results=results, feedback=feedback)
            
        except Exception as e:
            scenario.complete(False, str(e))
        
        return scenario
    
    async def uat_system_performance(self) -> UATScenario:
        """UAT: Test system performance and responsiveness"""
        scenario = self.create_scenario(
            "System Performance",
            "Test system response times and performance under load",
            "performance"
        )
        scenario.start()
        
        try:
            # Test response times for key operations
            performance_tests = []
            
            # Test 1: AI Response Time
            start_time = time.time()
            try:
                from atlas_ai_core import AtlasConversationalEngine
                ai_engine = AtlasConversationalEngine()
                ai_response_time = time.time() - start_time
                performance_tests.append({
                    "test": "AI Engine Initialization",
                    "duration": ai_response_time,
                    "acceptable": ai_response_time < 10.0,  # Should initialize within 10 seconds
                    "threshold": "< 10 seconds"
                })
            except Exception as e:
                performance_tests.append({
                    "test": "AI Engine Initialization",
                    "duration": None,
                    "acceptable": False,
                    "error": str(e)
                })
            
            # Test 2: Trading Engine Response Time
            start_time = time.time()
            try:
                from atlas_trading_core import TradingGodEngine
                trading_engine = TradingGodEngine()
                trading_response_time = time.time() - start_time
                performance_tests.append({
                    "test": "Trading Engine Initialization",
                    "duration": trading_response_time,
                    "acceptable": trading_response_time < 5.0,  # Should initialize within 5 seconds
                    "threshold": "< 5 seconds"
                })
            except Exception as e:
                performance_tests.append({
                    "test": "Trading Engine Initialization",
                    "duration": None,
                    "acceptable": False,
                    "error": str(e)
                })
            
            # Test 3: Scanner Response Time
            start_time = time.time()
            try:
                from atlas_lee_method import LeeMethodScanner
                scanner = LeeMethodScanner()
                scanner_response_time = time.time() - start_time
                performance_tests.append({
                    "test": "Scanner Initialization",
                    "duration": scanner_response_time,
                    "acceptable": scanner_response_time < 3.0,  # Should initialize within 3 seconds
                    "threshold": "< 3 seconds"
                })
            except Exception as e:
                performance_tests.append({
                    "test": "Scanner Initialization",
                    "duration": None,
                    "acceptable": False,
                    "error": str(e)
                })
            
            acceptable_tests = len([t for t in performance_tests if t.get("acceptable", False)])
            
            results = {
                "total_performance_tests": len(performance_tests),
                "acceptable_performance": acceptable_tests,
                "performance_tests": performance_tests
            }
            
            success = acceptable_tests >= len(performance_tests) * 0.7  # 70% acceptable performance
            feedback = f"✅ Performance acceptable ({acceptable_tests}/{len(performance_tests)} tests)" if success else f"❌ Performance issues ({acceptable_tests}/{len(performance_tests)} tests)"
            
            scenario.complete(success, results=results, feedback=feedback)
            
        except Exception as e:
            scenario.complete(False, str(e))
        
        return scenario
    
    async def run_comprehensive_uat(self) -> Dict[str, Any]:
        """Run comprehensive User Acceptance Testing"""
        logger.info("="*80)
        logger.info("A.T.L.A.S. USER ACCEPTANCE TESTING STARTED")
        logger.info("="*80)
        
        # Define UAT scenarios
        uat_methods = [
            self.uat_trading_paper_mode_verification,
            self.uat_ai_conversation_flow,
            self.uat_market_scanner_functionality,
            self.uat_risk_management_validation,
            self.uat_system_performance
        ]
        
        # Run all UAT scenarios
        for uat_method in uat_methods:
            try:
                logger.info(f"Running {uat_method.__name__}...")
                await uat_method()
            except Exception as e:
                logger.error(f"UAT scenario {uat_method.__name__} failed: {e}")
        
        # Generate UAT summary
        total_scenarios = len(self.scenarios)
        passed_scenarios = len([s for s in self.scenarios if s.status == "PASSED"])
        failed_scenarios = len([s for s in self.scenarios if s.status == "FAILED"])
        
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        # Category breakdown
        category_stats = {}
        for category_key, category_name in self.categories.items():
            category_scenarios = [s for s in self.scenarios if s.category == category_key]
            category_stats[category_name] = {
                "total": len(category_scenarios),
                "passed": len([s for s in category_scenarios if s.status == "PASSED"]),
                "failed": len([s for s in category_scenarios if s.status == "FAILED"])
            }
        
        success_rate = (passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0
        
        results = {
            "uat_summary": {
                "total_scenarios": total_scenarios,
                "passed_scenarios": passed_scenarios,
                "failed_scenarios": failed_scenarios,
                "success_rate": success_rate,
                "total_duration": total_duration,
                "status": "PASSED" if failed_scenarios == 0 else "FAILED"
            },
            "category_breakdown": category_stats,
            "scenario_details": [
                {
                    "name": s.name,
                    "description": s.description,
                    "category": s.category,
                    "status": s.status,
                    "duration": s.duration,
                    "error": s.error,
                    "results": s.results,
                    "user_feedback": s.user_feedback
                }
                for s in self.scenarios
            ],
            "recommendations": self._generate_uat_recommendations(success_rate, failed_scenarios)
        }
        
        # Save UAT report
        with open("user_acceptance_test_report.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        return results
    
    def _generate_uat_recommendations(self, success_rate: float, failed_scenarios: int) -> List[str]:
        """Generate UAT recommendations"""
        recommendations = []
        
        if failed_scenarios == 0:
            recommendations.append("✅ All UAT scenarios passed! System ready for production")
            recommendations.append("🚀 Proceed with performance optimization")
            recommendations.append("📋 Begin production environment setup")
        else:
            recommendations.append(f"❌ {failed_scenarios} UAT scenarios failed - address before production")
            recommendations.append("🔍 Review failed scenario details and fix issues")
            recommendations.append("🔄 Re-run UAT after fixes")
        
        if success_rate >= 90:
            recommendations.append("🎯 Excellent user experience - system is production-ready")
        elif success_rate >= 75:
            recommendations.append("⚠️ Good user experience - minor issues to address")
        else:
            recommendations.append("🚨 Poor user experience - significant issues need attention")
        
        return recommendations


async def main():
    """Main UAT execution"""
    uat_framework = AtlasUATFramework()
    results = await uat_framework.run_comprehensive_uat()
    
    # Print UAT summary
    print("\n" + "="*80)
    print("A.T.L.A.S. USER ACCEPTANCE TEST RESULTS")
    print("="*80)
    print(f"Total Scenarios: {results['uat_summary']['total_scenarios']}")
    print(f"Passed: {results['uat_summary']['passed_scenarios']}")
    print(f"Failed: {results['uat_summary']['failed_scenarios']}")
    print(f"Success Rate: {results['uat_summary']['success_rate']:.1f}%")
    print(f"Duration: {results['uat_summary']['total_duration']:.2f} seconds")
    print(f"Status: {results['uat_summary']['status']}")
    
    print(f"\nCategory Breakdown:")
    for category, stats in results['category_breakdown'].items():
        print(f"  {category}: {stats['passed']}/{stats['total']} passed")
    
    print(f"\nScenario Details:")
    for scenario in results['scenario_details']:
        status_icon = "✅" if scenario['status'] == "PASSED" else "❌"
        print(f"  {status_icon} {scenario['name']} ({scenario['category']}) - {scenario['duration']:.2f}s")
        if scenario['user_feedback']:
            print(f"    {scenario['user_feedback']}")
        if scenario['status'] == "FAILED" and scenario['error']:
            print(f"    Error: {scenario['error']}")
    
    print(f"\nRecommendations:")
    for rec in results['recommendations']:
        print(f"  {rec}")
    
    print("="*80)
    
    return results['uat_summary']['status'] == "PASSED"


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
