{"audit_timestamp": "2025-07-20T11:55:30.413070", "audit_duration_seconds": 0.255052, "security_score": 0, "security_level": "CRITICAL", "total_issues": 459, "critical_issues_count": 0, "high_issues_count": 432, "checks": {"hardcoded_secrets": {"total_files_scanned": 60, "files_with_secrets": 1, "total_secret_issues": 3, "findings": {"security_audit.py": [{"category": "database_urls", "pattern": "postgresql://[^\"\\s]+", "match": "postgresql://[^", "line": 51, "severity": "HIGH"}, {"category": "database_urls", "pattern": "mysql://[^\"\\s]+", "match": "mysql://[^", "line": 52, "severity": "HIGH"}, {"category": "database_urls", "pattern": "mongodb://[^\"\\s]+", "match": "mongodb://[^", "line": 53, "severity": "HIGH"}]}, "security_score_impact": -20}, "input_validation": {"total_validation_issues": 426, "issues": [{"file": "analyze_scanner_config.py", "line": 85, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% (", "severity": "HIGH"}, {"file": "analyze_scanner_config.py", "line": 96, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 62, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 62, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 62, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 62, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Z", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 63, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 63, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 63, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 63, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Z", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Z", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "apply_scanner_fixes.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 42, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 43, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 44, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 45, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 46, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 87, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 759, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 775, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1171, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1193, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1208, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1379, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1379, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% l", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1383, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1384, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1387, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1388, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1392, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1394, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1394, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1404, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1404, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% l", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1408, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1409, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1412, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1413, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1417, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1419, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1419, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1432, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1432, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% l", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1436, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1437, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1440, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1441, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1445, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1447, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1447, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1561, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1600, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% O", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1604, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% a", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1605, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1617, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% b", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1619, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1678, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1680, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% R", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1689, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1693, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 1944, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2003, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2019, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2175, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2177, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2196, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2197, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2206, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2207, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "atlas_ai_core.py", "line": 2211, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 207, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 208, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 211, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 212, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 215, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 216, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 488, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_alert_manager.py", "line": 489, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_alternative_data.py", "line": 546, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_autonomous_agents.py", "line": 112, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n        s", "severity": "HIGH"}, {"file": "atlas_autonomous_agents.py", "line": 281, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                    '", "severity": "HIGH"}, {"file": "atlas_autonomous_agents.py", "line": 513, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n        s", "severity": "HIGH"}, {"file": "atlas_autonomous_agents.py", "line": 574, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_causal_reasoning.py", "line": 382, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_causal_reasoning.py", "line": 384, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_database.py", "line": 232, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(row['metadata'])", "severity": "HIGH"}, {"file": "atlas_data_fusion.py", "line": 563, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 122, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% R", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 123, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 125, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% R", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 134, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 138, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 197, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_education.py", "line": 262, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_ethical_ai.py", "line": 162, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% d", "severity": "HIGH"}, {"file": "atlas_ethical_ai.py", "line": 328, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ethical_ai.py", "line": 333, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ethical_ai.py", "line": 337, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ethical_ai.py", "line": 1046, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 328, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% 1", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 493, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 495, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 497, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 508, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_explainable_ai.py", "line": 672, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% 1", "severity": "HIGH"}, {"file": "atlas_global_markets.py", "line": 244, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1647, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(\n                original_analysis=json.dumps(original_result, indent=2)", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1693, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(\n                original_analysis=json.dumps(original_result, indent=2)", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1899, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(\n                original_analysis=json.dumps(original_result, indent=2)", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1485, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1486, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 1487, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_grok_integration.py", "line": 685, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(content)", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 4, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%+", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 101, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 101, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 101, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 101, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 190, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 328, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% b", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 342, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 424, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 424, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 452, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% b", "severity": "HIGH"}, {"file": "atlas_grok_trading_strategies.py", "line": 527, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 189, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 525, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 527, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% f", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 529, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% f", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 531, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 533, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% f", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 539, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% f", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 542, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% f", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 544, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n\n        e", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 646, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 647, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1113, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1115, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            i", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1117, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            i", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1119, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            i", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1121, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n\n            #", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1123, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1127, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1131, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1135, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n\n        e", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1463, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1467, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1475, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1479, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1483, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_lee_method.py", "line": 1490, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n\n        e", "severity": "HIGH"}, {"file": "atlas_market_core.py", "line": 1180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% b", "severity": "HIGH"}, {"file": "atlas_market_core.py", "line": 1180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 451, "pattern": "eval\\s*\\(", "description": "Code execution vulnerability", "code_snippet": "eval(", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 481, "pattern": "eval\\s*\\(", "description": "Code execution vulnerability", "code_snippet": "eval(", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 157, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 157, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 185, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% d", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 190, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% d", "severity": "HIGH"}, {"file": "atlas_ml_analytics.py", "line": 922, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 47, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                '", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 48, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                '", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 49, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                '", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 51, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            }", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 135, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 144, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 153, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_monitoring.py", "line": 291, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 376, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 684, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% 1", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_news_insights_engine.py", "line": 1085, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 96, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 97, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% d", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 194, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% v", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 252, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 361, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% O", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 370, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% n", "severity": "HIGH"}, {"file": "atlas_options.py", "line": 429, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% v", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 4, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%+", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 55, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%+", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 70, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 73, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 90, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%+", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 270, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%+", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 274, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_performance_monitor.py", "line": 274, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 100, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 101, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 102, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 103, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 138, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 139, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 140, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 141, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 142, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 148, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 149, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 150, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 151, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 157, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 158, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 159, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 160, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 161, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 168, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 169, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 170, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 171, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 177, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 178, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 179, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 180, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 186, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 187, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 188, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 189, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 195, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 196, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 197, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 198, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 204, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 205, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 206, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 207, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 213, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 214, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 215, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 216, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 217, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 218, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_progress_tracker.py", "line": 425, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 50, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 51, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% v", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 134, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% c", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 153, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 161, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 347, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% V", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 348, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 350, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% v", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 379, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 379, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 386, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 393, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_realtime.py", "line": 400, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_realtime_monitor.py", "line": 288, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%'", "severity": "HIGH"}, {"file": "atlas_realtime_monitor.py", "line": 292, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%'", "severity": "HIGH"}, {"file": "atlas_realtime_monitor.py", "line": 297, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%'", "severity": "HIGH"}, {"file": "atlas_realtime_monitor.py", "line": 415, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_realtime_monitor.py", "line": 417, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 36, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 37, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 38, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 193, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% V", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 197, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 209, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                '", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 210, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n                '", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 211, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            }", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 248, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 295, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% a", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 296, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% V", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 324, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% e", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 324, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 326, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% a", "severity": "HIGH"}, {"file": "atlas_risk_core.py", "line": 480, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_secrets_manager.py", "line": 92, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(decrypted_data.decode()", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 3, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% E", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% E", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 526, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% E", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 537, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% E", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 549, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_security.py", "line": 555, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 342, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 1724, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 1724, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 1724, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 1724, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 568, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(data)", "severity": "HIGH"}, {"file": "atlas_server.py", "line": 998, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(data)", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 85, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% b", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 88, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 89, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 90, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 110, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_strategies.py", "line": 323, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_terminal_streamer.py", "line": 229, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(record)", "severity": "HIGH"}, {"file": "atlas_terminal_streamer.py", "line": 268, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_terminal_streamer.py", "line": 268, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_terminal_streamer.py", "line": 268, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_terminal_streamer.py", "line": 268, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_testing.py", "line": 463, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% k", "severity": "HIGH"}, {"file": "atlas_theory_of_mind.py", "line": 425, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_theory_of_mind.py", "line": 425, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\n            v", "severity": "HIGH"}, {"file": "atlas_theory_of_mind.py", "line": 649, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 55, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 85, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 86, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% s", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 109, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 111, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% w", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 111, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% l", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 115, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 116, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 119, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 120, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% t", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 124, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% -", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 126, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% r", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 126, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 206, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 207, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 219, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 514, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_trading_core.py", "line": 549, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% m", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 40, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 40, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 40, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 40, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 41, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 390, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "atlas_utils.py", "line": 380, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(json_str)", "severity": "HIGH"}, {"file": "atlas_video_processor.py", "line": 293, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% y", "severity": "HIGH"}, {"file": "atlas_video_processor.py", "line": 294, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%.", "severity": "HIGH"}, {"file": "atlas_video_processor.py", "line": 407, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%'", "severity": "HIGH"}, {"file": "atlas_video_processor.py", "line": 408, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%'", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 185, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(symbols=symbols_str, query=search_query.query)", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 187, "pattern": "\\.format\\s*\\([^)]*\\)", "description": "String formatting without validation", "code_snippet": ".format(query=search_query.query)", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "atlas_web_search_service.py", "line": 465, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "comprehensive_system_validation.py", "line": 16, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "comprehensive_system_validation.py", "line": 16, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "comprehensive_system_validation.py", "line": 16, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 275, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 275, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 275, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 275, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "config.py", "line": 276, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 279, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "config.py", "line": 280, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "debug_progress.py", "line": 40, "pattern": "json\\.loads\\s*\\([^)]*\\)", "description": "JSON parsing without validation", "code_snippet": "json.loads(message)", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 179, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 181, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "grok_advanced_features_example.py", "line": 258, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "grok_performance_optimizer.py", "line": 291, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "grok_performance_optimizer.py", "line": 295, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%}", "severity": "HIGH"}, {"file": "grok_resilience_manager.py", "line": 143, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% j", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 69, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 70, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% v", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 71, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% p", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Y", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%m", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%d", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 176, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "grok_usage_examples.py", "line": 473, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\"", "severity": "HIGH"}, {"file": "security_audit.py", "line": 17, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "security_audit.py", "line": 17, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "security_audit.py", "line": 17, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%(", "severity": "HIGH"}, {"file": "security_audit.py", "line": 112, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%\\", "severity": "HIGH"}, {"file": "security_audit.py", "line": 112, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%]", "severity": "HIGH"}, {"file": "test_critical_vulnerabilities.py", "line": 287, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "% o", "severity": "HIGH"}, {"file": "validate_api_endpoints.py", "line": 251, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%)", "severity": "HIGH"}, {"file": "validate_fixes.py", "line": 27, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%H", "severity": "HIGH"}, {"file": "validate_fixes.py", "line": 27, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%M", "severity": "HIGH"}, {"file": "validate_fixes.py", "line": 27, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%S", "severity": "HIGH"}, {"file": "validate_fixes.py", "line": 27, "pattern": "%\\s*[^%]", "description": "String interpolation without validation", "code_snippet": "%Z", "severity": "HIGH"}], "security_score_impact": -2130}, "error_handling": {"total_error_issues": 24, "issues": [{"file": "atlas_ai_core.py", "line": 508, "description": "API key potentially logged", "code_snippet": "logger.warning(\"[WARN] Grok API key not found\")", "severity": "MEDIUM"}, {"file": "atlas_ai_core.py", "line": 517, "description": "API key potentially logged", "code_snippet": "logger.warning(\"[WARN] OpenAI API key not found\")", "severity": "MEDIUM"}, {"file": "atlas_market_core.py", "line": 313, "description": "API key potentially logged", "code_snippet": "logger.info(f\"[OK] Alpaca client initialized with key: {fresh_config.get('api_key', 'None')", "severity": "MEDIUM"}, {"file": "atlas_monitoring.py", "line": 84, "description": "Bare except clause - may hide errors", "code_snippet": "except:", "severity": "MEDIUM"}, {"file": "atlas_orchestrator.py", "line": 497, "description": "Bare except clause - may hide errors", "code_snippet": "except:", "severity": "MEDIUM"}, {"file": "atlas_orchestrator.py", "line": 940, "description": "Bare except clause - may hide errors", "code_snippet": "except:", "severity": "MEDIUM"}, {"file": "atlas_quantum_optimizer.py", "line": 645, "description": "API key potentially logged", "code_snippet": "logger.error(f\"Cache key generation failed: {e}\")", "severity": "MEDIUM"}, {"file": "atlas_realtime_monitor.py", "line": 632, "description": "Bare except clause - may hide errors", "code_snippet": "except:", "severity": "MEDIUM"}, {"file": "atlas_secrets_manager.py", "line": 252, "description": "Password logged", "code_snippet": "logger.info(\"No master password provided. Using environment variables only.\")", "severity": "MEDIUM"}, {"file": "atlas_secrets_manager.py", "line": 125, "description": "API key potentially logged", "code_snippet": "logger.error(f\"Error retrieving secret {category}.{key}: {e}\")", "severity": "MEDIUM"}, {"file": "atlas_secrets_manager.py", "line": 152, "description": "API key potentially logged", "code_snippet": "logger.info(f\"Secret {category}.{key} updated successfully\")", "severity": "MEDIUM"}, {"file": "atlas_secrets_manager.py", "line": 156, "description": "API key potentially logged", "code_snippet": "logger.error(f\"Failed to set secret {category}.{key}: {e}\")", "severity": "MEDIUM"}, {"file": "atlas_security.py", "line": 210, "description": "API key potentially logged", "code_snippet": "logger.error(f\"API key validation failed: {e}\")", "severity": "MEDIUM"}, {"file": "atlas_server.py", "line": 1018, "description": "Bare except clause - may hide errors", "code_snippet": "except:", "severity": "MEDIUM"}, {"file": "atlas_utils.py", "line": 265, "description": "API key potentially logged", "code_snippet": "logger.error(f\"API key validation error: {e}\")", "severity": "MEDIUM"}, {"file": "atlas_video_processor.py", "line": 394, "description": "API key potentially logged", "code_snippet": "logger.error(f\"Key phrase extraction failed: {e}\")", "severity": "MEDIUM"}, {"file": "debug_progress.py", "line": 55, "description": "Full traceback exposed", "code_snippet": "traceback.print_exc()", "severity": "MEDIUM"}, {"file": "grok_performance_optimizer.py", "line": 110, "description": "API key potentially logged", "code_snippet": "logger.debug(f\"<PERSON><PERSON> hit for key: {key[:16]}...\")", "severity": "MEDIUM"}, {"file": "grok_performance_optimizer.py", "line": 138, "description": "API key potentially logged", "code_snippet": "logger.debug(f\"Cached response for key: {key[:16]}...\")", "severity": "MEDIUM"}, {"file": "grok_performance_optimizer.py", "line": 153, "description": "API key potentially logged", "code_snippet": "logger.info(f\"Invalidated {len(keys_to_remove)", "severity": "MEDIUM"}, {"file": "grok_resilience_manager.py", "line": 349, "description": "API key potentially logged", "code_snippet": "logger.warning(f\"Circuit breaker {circuit_key} is open, using fallback\")", "severity": "MEDIUM"}, {"file": "news_insights_examples.py", "line": 124, "description": "Exception details printed to console", "code_snippet": "print(f\"❌ Exception: {e}\")", "severity": "MEDIUM"}, {"file": "news_insights_examples.py", "line": 140, "description": "Exception details printed to console", "code_snippet": "print(f\"❌ Exception: {e}\")", "severity": "MEDIUM"}, {"file": "news_insights_examples.py", "line": 158, "description": "Exception details printed to console", "code_snippet": "print(f\"❌ Exception: {e}\")", "severity": "MEDIUM"}], "security_score_impact": -48}, "file_permissions": {"total_permission_issues": 6, "issues": [{"file": ".env", "permissions": "666", "issue": "World-readable sensitive file", "severity": "HIGH"}, {"file": ".env", "permissions": "666", "issue": "Group-writable sensitive file", "severity": "MEDIUM"}, {"file": "config.py", "permissions": "666", "issue": "World-readable sensitive file", "severity": "HIGH"}, {"file": "config.py", "permissions": "666", "issue": "Group-writable sensitive file", "severity": "MEDIUM"}, {"file": "atlas_secrets_manager.py", "permissions": "666", "issue": "World-readable sensitive file", "severity": "HIGH"}, {"file": "atlas_secrets_manager.py", "permissions": "666", "issue": "Group-writable sensitive file", "severity": "MEDIUM"}], "security_score_impact": -30}, "dependency_security": {"total_dependency_issues": 0, "issues": [], "security_score_impact": 0}, "trading_security": {"total_trading_issues": 0, "issues": [], "security_score_impact": 0}}, "critical_issues": [], "recommendations": ["⚠️ Address HIGH severity security issues", "🔍 Implement comprehensive input validation", "📝 Review error handling to prevent information disclosure", "🚫 DO NOT DEPLOY to production until security score improves", "🔐 Conduct thorough security review with external auditor"]}