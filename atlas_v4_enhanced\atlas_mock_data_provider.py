"""
A.T.L.A.S. Mock Data Provider
Provides realistic mock market data when external APIs fail or are rate limited
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from atlas_enhanced_market_data import MarketQuote, HistoricalData

logger = logging.getLogger(__name__)


class MockDataProvider:
    """Provides realistic mock market data for testing and fallback scenarios"""
    
    def __init__(self):
        # Base prices for common symbols (realistic as of 2024)
        self.base_prices = {
            'AAPL': 175.00,
            'MSFT': 350.00,
            'GOOGL': 140.00,
            'AMZN': 145.00,
            'TSLA': 200.00,
            'META': 320.00,
            'NVDA': 450.00,
            'NFLX': 400.00,
            'ADBE': 500.00,
            'CRM': 220.00,
            'ORCL': 110.00,
            'INTC': 45.00,
            'AMD': 120.00,
            'QCOM': 150.00,
            'AVGO': 900.00,
            'TXN': 170.00,
            'AMAT': 140.00,
            'MU': 80.00,
            'PYPL': 60.00,
            'SQ': 70.00
        }
        
        # Market volatility patterns
        self.volatility_patterns = {
            'low': 0.02,    # 2% daily volatility
            'medium': 0.05, # 5% daily volatility
            'high': 0.08    # 8% daily volatility
        }
        
        # Symbol volatility classifications
        self.symbol_volatility = {
            'AAPL': 'medium', 'MSFT': 'low', 'GOOGL': 'medium',
            'AMZN': 'medium', 'TSLA': 'high', 'META': 'high',
            'NVDA': 'high', 'NFLX': 'medium', 'ADBE': 'medium',
            'CRM': 'medium', 'ORCL': 'low', 'INTC': 'medium',
            'AMD': 'high', 'QCOM': 'medium', 'AVGO': 'medium',
            'TXN': 'low', 'AMAT': 'medium', 'MU': 'high',
            'PYPL': 'high', 'SQ': 'high'
        }
    
    def _get_base_price(self, symbol: str) -> float:
        """Get base price for symbol"""
        return self.base_prices.get(symbol, 100.0)  # Default $100
    
    def _get_volatility(self, symbol: str) -> float:
        """Get volatility for symbol"""
        vol_type = self.symbol_volatility.get(symbol, 'medium')
        return self.volatility_patterns[vol_type]
    
    def generate_mock_quote(self, symbol: str) -> MarketQuote:
        """Generate realistic mock quote data"""
        try:
            base_price = self._get_base_price(symbol)
            volatility = self._get_volatility(symbol)
            
            # Generate realistic price movement
            price_change_percent = np.random.normal(0, volatility)
            price_change = base_price * price_change_percent
            current_price = base_price + price_change
            
            # Ensure price is positive
            current_price = max(current_price, base_price * 0.5)
            
            # Generate OHLC data
            daily_range = current_price * volatility * 0.5
            high = current_price + np.random.uniform(0, daily_range)
            low = current_price - np.random.uniform(0, daily_range)
            open_price = low + np.random.uniform(0, high - low)
            
            # Generate volume (realistic ranges)
            base_volume = {
                'AAPL': 50000000, 'MSFT': 30000000, 'GOOGL': 25000000,
                'AMZN': 35000000, 'TSLA': 80000000, 'META': 20000000,
                'NVDA': 40000000, 'NFLX': 15000000
            }.get(symbol, 10000000)
            
            volume = int(base_volume * np.random.uniform(0.5, 2.0))
            
            return MarketQuote(
                symbol=symbol,
                price=round(current_price, 2),
                change=round(price_change, 2),
                change_percent=round(price_change_percent * 100, 2),
                volume=volume,
                high=round(high, 2),
                low=round(low, 2),
                open=round(open_price, 2),
                timestamp=datetime.now(),
                source='mock_data'
            )
            
        except Exception as e:
            logger.error(f"Error generating mock quote for {symbol}: {e}")
            # Return minimal quote
            return MarketQuote(
                symbol=symbol,
                price=100.0,
                change=0.0,
                change_percent=0.0,
                volume=1000000,
                high=100.0,
                low=100.0,
                open=100.0,
                timestamp=datetime.now(),
                source='mock_data_fallback'
            )
    
    def generate_mock_historical_data(self, symbol: str, periods: int = 100) -> HistoricalData:
        """Generate realistic mock historical data"""
        try:
            base_price = self._get_base_price(symbol)
            volatility = self._get_volatility(symbol)
            
            # Generate date range
            end_date = datetime.now()
            dates = [end_date - timedelta(days=i) for i in range(periods)]
            dates.reverse()
            
            # Generate price series with realistic trends
            prices = []
            current_price = base_price
            
            # Add some trend (slight upward bias for most stocks)
            trend = np.random.uniform(-0.001, 0.002)  # -0.1% to +0.2% daily trend
            
            for i, date in enumerate(dates):
                # Random walk with trend
                daily_return = np.random.normal(trend, volatility)
                current_price *= (1 + daily_return)
                
                # Ensure price doesn't go too low
                current_price = max(current_price, base_price * 0.3)
                
                prices.append(current_price)
            
            # Generate OHLCV data
            data_rows = []
            for i, (date, price) in enumerate(zip(dates, prices)):
                # Generate realistic OHLC
                daily_volatility = volatility * 0.5
                high = price * (1 + np.random.uniform(0, daily_volatility))
                low = price * (1 - np.random.uniform(0, daily_volatility))
                
                # Open is previous close with some gap
                if i == 0:
                    open_price = price
                else:
                    gap = np.random.normal(0, volatility * 0.2)
                    open_price = prices[i-1] * (1 + gap)
                    open_price = max(low, min(high, open_price))
                
                # Volume with realistic patterns
                base_volume = {
                    'AAPL': 50000000, 'MSFT': 30000000, 'GOOGL': 25000000,
                    'AMZN': 35000000, 'TSLA': 80000000, 'META': 20000000,
                    'NVDA': 40000000, 'NFLX': 15000000
                }.get(symbol, 10000000)
                
                volume = int(base_volume * np.random.uniform(0.3, 2.5))
                
                data_rows.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(price, 2),
                    'volume': volume
                })
            
            df = pd.DataFrame(data_rows)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            return HistoricalData(
                symbol=symbol,
                data=df,
                timeframe='1d',
                source='mock_data',
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error generating mock historical data for {symbol}: {e}")
            # Return minimal data
            df = pd.DataFrame({
                'date': [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)],
                'open': [100.0] * 30,
                'high': [105.0] * 30,
                'low': [95.0] * 30,
                'close': [100.0] * 30,
                'volume': [1000000] * 30
            })
            
            return HistoricalData(
                symbol=symbol,
                data=df,
                timeframe='1d',
                source='mock_data_fallback',
                timestamp=datetime.now()
            )
    
    def generate_batch_quotes(self, symbols: List[str]) -> Dict[str, MarketQuote]:
        """Generate mock quotes for multiple symbols"""
        quotes = {}
        for symbol in symbols:
            try:
                quotes[symbol] = self.generate_mock_quote(symbol)
            except Exception as e:
                logger.error(f"Error generating mock quote for {symbol}: {e}")
                quotes[symbol] = None
        return quotes
    
    def is_market_hours(self) -> bool:
        """Mock market hours check (always return True for testing)"""
        return True
    
    def get_market_status(self) -> str:
        """Get mock market status"""
        return "OPEN"


# Global mock data provider
mock_data_provider = MockDataProvider()
