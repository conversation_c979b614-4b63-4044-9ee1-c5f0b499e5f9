"""
A.T.L.A.S. Enhanced Market Data Manager
Multi-source data aggregation with intelligent fallback and caching
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import pandas as pd
import numpy as np
import yfinance as yf
from atlas_rate_limiter import rate_limiter, APIProvider
from atlas_mock_data_provider import mock_data_provider

logger = logging.getLogger(__name__)


@dataclass
class MarketQuote:
    """Standardized market quote"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float
    timestamp: datetime
    source: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'price': self.price,
            'change': self.change,
            'change_percent': self.change_percent,
            'volume': self.volume,
            'high': self.high,
            'low': self.low,
            'open': self.open,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source
        }


@dataclass
class HistoricalData:
    """Standardized historical data"""
    symbol: str
    data: pd.DataFrame
    timeframe: str
    source: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'data': self.data.to_dict('records'),
            'timeframe': self.timeframe,
            'source': self.source,
            'timestamp': self.timestamp.isoformat()
        }


class EnhancedMarketDataManager:
    """Enhanced market data manager with multiple sources and intelligent fallback"""
    
    def __init__(self):
        self.data_sources = {
            'fmp': self._get_fmp_data,
            'alpaca': self._get_alpaca_data,
            'yfinance': self._get_yfinance_data,
            'polygon': self._get_polygon_data,
            'alpha_vantage': self._get_alpha_vantage_data
        }
        
        # Source priority (higher number = higher priority)
        self.source_priority = {
            'fmp': 5,
            'alpaca': 4,
            'yfinance': 3,
            'polygon': 2,
            'alpha_vantage': 1
        }
        
        # Source health tracking
        self.source_health = {source: 1.0 for source in self.data_sources}
        self.source_last_success = {source: time.time() for source in self.data_sources}
        self.source_failure_count = {source: 0 for source in self.data_sources}
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'fallback_usage': 0,
            'average_response_time': 0.0,
            'source_usage': {source: 0 for source in self.data_sources}
        }
        
        # Local cache for ultra-fast access
        self.quote_cache = {}
        self.historical_cache = {}
        self.cache_ttl = {}
        
        # Start rate limiter
        asyncio.create_task(rate_limiter.start_processing())
    
    def _update_source_health(self, source: str, success: bool):
        """Update source health based on success/failure"""
        if success:
            self.source_health[source] = min(1.0, self.source_health[source] + 0.1)
            self.source_last_success[source] = time.time()
            self.source_failure_count[source] = 0
        else:
            self.source_health[source] = max(0.1, self.source_health[source] - 0.2)
            self.source_failure_count[source] += 1
    
    def _get_source_score(self, source: str) -> float:
        """Calculate source score based on priority and health"""
        base_score = self.source_priority[source]
        health_multiplier = self.source_health[source]
        
        # Penalize sources that haven't succeeded recently
        time_penalty = min(1.0, (time.time() - self.source_last_success[source]) / 3600)  # 1 hour
        
        return base_score * health_multiplier * (1 - time_penalty * 0.5)
    
    def _get_ordered_sources(self) -> List[str]:
        """Get data sources ordered by score (best first)"""
        sources = list(self.data_sources.keys())
        sources.sort(key=self._get_source_score, reverse=True)
        return sources
    
    async def _get_fmp_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Financial Modeling Prep"""
        try:
            if data_type == 'quote':
                endpoint = f"quote/{symbol}"
                params = {'apikey': 'demo'}  # Use demo key for testing
                
                result = await rate_limiter.make_request(
                    APIProvider.FMP, endpoint, params, priority=1
                )
                
                if result and isinstance(result, list) and len(result) > 0:
                    data = result[0]
                    return MarketQuote(
                        symbol=symbol,
                        price=float(data.get('price', 0)),
                        change=float(data.get('change', 0)),
                        change_percent=float(data.get('changesPercentage', 0)),
                        volume=int(data.get('volume', 0)),
                        high=float(data.get('dayHigh', 0)),
                        low=float(data.get('dayLow', 0)),
                        open=float(data.get('open', 0)),
                        timestamp=datetime.now(),
                        source='fmp'
                    )
            
            elif data_type == 'historical':
                endpoint = f"historical-price-full/{symbol}"
                params = {'apikey': 'demo', 'timeseries': '30'}
                
                result = await rate_limiter.make_request(
                    APIProvider.FMP, endpoint, params, priority=2
                )
                
                if result and 'historical' in result:
                    df = pd.DataFrame(result['historical'])
                    if not df.empty:
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.sort_values('date')
                        return HistoricalData(
                            symbol=symbol,
                            data=df,
                            timeframe='1d',
                            source='fmp',
                            timestamp=datetime.now()
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"FMP data error for {symbol}: {e}")
            return None
    
    async def _get_alpaca_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpaca (paper trading)"""
        try:
            if data_type == 'quote':
                endpoint = f"stocks/{symbol}/quotes/latest"
                params = {}
                
                result = await rate_limiter.make_request(
                    APIProvider.ALPACA, endpoint, params, priority=1
                )
                
                if result and 'quote' in result:
                    quote = result['quote']
                    # Calculate derived values
                    bid = float(quote.get('bid_price', 0))
                    ask = float(quote.get('ask_price', 0))
                    price = (bid + ask) / 2 if bid and ask else bid or ask
                    
                    return MarketQuote(
                        symbol=symbol,
                        price=price,
                        change=0.0,  # Not provided by this endpoint
                        change_percent=0.0,
                        volume=int(quote.get('bid_size', 0) + quote.get('ask_size', 0)),
                        high=price,  # Approximate
                        low=price,   # Approximate
                        open=price,  # Approximate
                        timestamp=datetime.now(),
                        source='alpaca'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Alpaca data error for {symbol}: {e}")
            return None
    
    async def _get_yfinance_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Yahoo Finance (yfinance)"""
        try:
            # Use rate limiter for yfinance too
            cache_key = f"yfinance:{symbol}:{data_type}"
            
            if data_type == 'quote':
                # Check if we have recent data
                if cache_key in self.quote_cache:
                    cached_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cached_time < 30:  # 30 second cache
                        return self.quote_cache[cache_key]
                
                # Fetch new data with rate limiting
                await asyncio.sleep(0.5)  # Basic rate limiting for yfinance
                
                ticker = yf.Ticker(symbol)
                info = ticker.info
                
                if info and 'regularMarketPrice' in info:
                    quote = MarketQuote(
                        symbol=symbol,
                        price=float(info.get('regularMarketPrice', 0)),
                        change=float(info.get('regularMarketChange', 0)),
                        change_percent=float(info.get('regularMarketChangePercent', 0)),
                        volume=int(info.get('regularMarketVolume', 0)),
                        high=float(info.get('regularMarketDayHigh', 0)),
                        low=float(info.get('regularMarketDayLow', 0)),
                        open=float(info.get('regularMarketOpen', 0)),
                        timestamp=datetime.now(),
                        source='yfinance'
                    )
                    
                    # Cache the result
                    self.quote_cache[cache_key] = quote
                    self.cache_ttl[cache_key] = time.time()
                    
                    return quote
            
            elif data_type == 'historical':
                # Check cache
                if cache_key in self.historical_cache:
                    cached_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cached_time < 300:  # 5 minute cache
                        return self.historical_cache[cache_key]
                
                await asyncio.sleep(1.0)  # Rate limiting for historical data
                
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="30d", interval="1d")
                
                if not hist.empty:
                    # Reset index to get date as column
                    hist = hist.reset_index()
                    hist.columns = [col.lower() for col in hist.columns]
                    
                    historical = HistoricalData(
                        symbol=symbol,
                        data=hist,
                        timeframe='1d',
                        source='yfinance',
                        timestamp=datetime.now()
                    )
                    
                    # Cache the result
                    self.historical_cache[cache_key] = historical
                    self.cache_ttl[cache_key] = time.time()
                    
                    return historical
            
            return None
            
        except Exception as e:
            logger.error(f"YFinance data error for {symbol}: {e}")
            return None
    
    async def _get_polygon_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Polygon.io"""
        try:
            if data_type == 'quote':
                endpoint = f"last/stocks/{symbol}"
                params = {'apikey': 'demo'}
                
                result = await rate_limiter.make_request(
                    APIProvider.POLYGON, endpoint, params, priority=2
                )
                
                if result and 'results' in result:
                    data = result['results']
                    return MarketQuote(
                        symbol=symbol,
                        price=float(data.get('p', 0)),
                        change=0.0,  # Calculate if previous close available
                        change_percent=0.0,
                        volume=int(data.get('v', 0)),
                        high=float(data.get('h', 0)),
                        low=float(data.get('l', 0)),
                        open=float(data.get('o', 0)),
                        timestamp=datetime.now(),
                        source='polygon'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Polygon data error for {symbol}: {e}")
            return None
    
    async def _get_alpha_vantage_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpha Vantage"""
        try:
            if data_type == 'quote':
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol,
                    'apikey': 'demo'
                }
                
                result = await rate_limiter.make_request(
                    APIProvider.ALPHA_VANTAGE, "", params, priority=3
                )
                
                if result and 'Global Quote' in result:
                    data = result['Global Quote']
                    return MarketQuote(
                        symbol=symbol,
                        price=float(data.get('05. price', 0)),
                        change=float(data.get('09. change', 0)),
                        change_percent=float(data.get('10. change percent', '0%').rstrip('%')),
                        volume=int(data.get('06. volume', 0)),
                        high=float(data.get('03. high', 0)),
                        low=float(data.get('04. low', 0)),
                        open=float(data.get('02. open', 0)),
                        timestamp=datetime.now(),
                        source='alpha_vantage'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Alpha Vantage data error for {symbol}: {e}")
            return None
    
    async def get_quote(self, symbol: str, timeout: float = 10.0) -> Optional[MarketQuote]:
        """Get real-time quote with intelligent fallback"""
        start_time = time.time()
        self.metrics['total_requests'] += 1
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} quote")
                
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'quote'),
                    timeout=timeout / len(sources)
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['successful_requests'] += 1
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    self.metrics['average_response_time'] = (
                        (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + response_time) /
                        self.metrics['successful_requests']
                    )
                    
                    logger.info(f"Successfully got {symbol} quote from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} quote from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} quote from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.warning(f"Failed to get quote for {symbol} from all sources, using mock data")

        # FALLBACK: Use mock data when all sources fail
        try:
            mock_quote = mock_data_provider.generate_mock_quote(symbol)
            self.metrics['successful_requests'] += 1
            self.metrics['source_usage']['mock_data'] = self.metrics['source_usage'].get('mock_data', 0) + 1

            response_time = time.time() - start_time
            self.metrics['average_response_time'] = (
                (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + response_time) /
                self.metrics['successful_requests']
            )

            logger.info(f"Successfully generated mock quote for {symbol} in {response_time:.2f}s")
            return mock_quote

        except Exception as e:
            logger.error(f"Failed to generate mock quote for {symbol}: {e}")
            return None
    
    async def get_historical_data(self, symbol: str, timeframe: str = '1d', 
                                 timeout: float = 15.0) -> Optional[HistoricalData]:
        """Get historical data with intelligent fallback"""
        start_time = time.time()
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} historical data")
                
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'historical'),
                    timeout=timeout / len(sources)
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    logger.info(f"Successfully got {symbol} historical data from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} historical data from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} historical data from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.error(f"Failed to get historical data for {symbol} from all sources")
        return None
    
    async def get_multiple_quotes(self, symbols: List[str], timeout: float = 30.0) -> Dict[str, Optional[MarketQuote]]:
        """Get multiple quotes efficiently with batch processing"""
        start_time = time.time()
        results = {}
        
        # Create tasks for all symbols
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self.get_quote(symbol, timeout / len(symbols)))
            tasks.append((symbol, task))
        
        # Wait for all tasks with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.warning("Timeout in batch quote request")
        
        # Collect results
        for symbol, task in tasks:
            try:
                if task.done():
                    result = task.result()
                    results[symbol] = result
                else:
                    task.cancel()
                    results[symbol] = None
            except Exception as e:
                logger.error(f"Error getting quote for {symbol}: {e}")
                results[symbol] = None
        
        response_time = time.time() - start_time
        successful_count = len([r for r in results.values() if r is not None])
        
        logger.info(f"Batch quote request completed: {successful_count}/{len(symbols)} successful in {response_time:.2f}s")
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            'source_health': self.source_health,
            'source_priority': self.source_priority,
            'cache_size': len(self.quote_cache) + len(self.historical_cache)
        }


# Global enhanced market data manager
enhanced_market_data = EnhancedMarketDataManager()
