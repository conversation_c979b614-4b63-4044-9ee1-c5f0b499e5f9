{"uat_summary": {"total_scenarios": 5, "passed_scenarios": 4, "failed_scenarios": 1, "success_rate": 80.0, "total_duration": 14.733688, "status": "FAILED"}, "category_breakdown": {"Trading Functionality": {"total": 1, "passed": 1, "failed": 0}, "AI Conversation": {"total": 1, "passed": 1, "failed": 0}, "Market Scanner": {"total": 1, "passed": 0, "failed": 1}, "Risk Management": {"total": 1, "passed": 1, "failed": 0}, "User Interface": {"total": 0, "passed": 0, "failed": 0}, "Performance & Responsiveness": {"total": 1, "passed": 1, "failed": 0}}, "scenario_details": [{"name": "Paper Trading Mode Verification", "description": "Verify that all trading operations are in paper trading mode only", "category": "trading", "status": "PASSED", "duration": 0.48240041732788086, "error": null, "results": {"paper_mode_enforced": true, "analysis_generated": true, "live_trading_blocked": true, "trading_engines_tested": 2}, "user_feedback": "✅ Paper trading mode properly enforced"}, {"name": "AI Conversation Flow", "description": "Test AI conversation capabilities and response quality", "category": "ai", "status": "PASSED", "duration": 3.5666332244873047, "error": null, "results": {"total_messages_tested": 5, "successful_responses": 5, "success_rate": 100.0, "responses": [{"message": "Hello, I'm new to trading", "response_received": true, "response_length": 362}, {"message": "What is the current price of AAPL?", "response_received": true, "response_length": 589}, {"message": "Should I buy TSLA stock?", "response_received": true, "response_length": 401}, {"message": "Explain the Lee Method", "response_received": true, "response_length": 913}, {"message": "What are the risks of options trading?", "response_received": true, "response_length": 913}]}, "user_feedback": "✅ AI conversation working (5/5 responses)"}, {"name": "Market Scanner Functionality", "description": "Test Lee Method scanner and market analysis capabilities", "category": "scanner", "status": "FAILED", "duration": 10.682563304901123, "error": null, "results": {"scanner_initialized": true, "symbols_tested": 3, "successful_scans": 0, "scan_results": [{"symbol": "AAPL", "scan_successful": false, "has_signal": false}, {"symbol": "MSFT", "scan_successful": false, "has_signal": false}, {"symbol": "GOOGL", "scan_successful": false, "error": "'LeeMethodSignal' object has no attribute 'get'"}]}, "user_feedback": "❌ Scanner issues (0/3 scans)"}, {"name": "Risk Management Validation", "description": "Test risk assessment and position sizing functionality", "category": "risk", "status": "PASSED", "duration": 0.0008180141448974609, "error": null, "results": {"positions_tested": 3, "successful_assessments": 3, "assessments": [{"symbol": "AAPL", "assessment_successful": true, "risk_score": 8.0, "position_size": 15000.0}, {"symbol": "MSFT", "assessment_successful": true, "risk_score": 8.0, "position_size": 15000.0}, {"symbol": "GOOGL", "assessment_successful": true, "risk_score": 8.0, "position_size": 62500.0}]}, "user_feedback": "✅ Risk management working (3/3 assessments)"}, {"name": "System Performance", "description": "Test system response times and performance under load", "category": "performance", "status": "PASSED", "duration": 0.00039124488830566406, "error": null, "results": {"total_performance_tests": 3, "acceptable_performance": 3, "performance_tests": [{"test": "AI Engine Initialization", "duration": 0.0001671314239501953, "acceptable": true, "threshold": "< 10 seconds"}, {"test": "Trading Engine Initialization", "duration": 4.00543212890625e-05, "acceptable": true, "threshold": "< 5 seconds"}, {"test": "Scanner Initialization", "duration": 0.0001773834228515625, "acceptable": true, "threshold": "< 3 seconds"}]}, "user_feedback": "✅ Performance acceptable (3/3 tests)"}], "recommendations": ["❌ 1 UAT scenarios failed - address before production", "🔍 Review failed scenario details and fix issues", "🔄 Re-run UAT after fixes", "⚠️ Good user experience - minor issues to address"]}