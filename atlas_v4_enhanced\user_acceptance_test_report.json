{"uat_summary": {"total_scenarios": 5, "passed_scenarios": 4, "failed_scenarios": 1, "success_rate": 80.0, "total_duration": 26.578802, "status": "FAILED"}, "category_breakdown": {"Trading Functionality": {"total": 1, "passed": 1, "failed": 0}, "AI Conversation": {"total": 1, "passed": 1, "failed": 0}, "Market Scanner": {"total": 1, "passed": 0, "failed": 1}, "Risk Management": {"total": 1, "passed": 1, "failed": 0}, "User Interface": {"total": 0, "passed": 0, "failed": 0}, "Performance & Responsiveness": {"total": 1, "passed": 1, "failed": 0}}, "scenario_details": [{"name": "Paper Trading Mode Verification", "description": "Verify that all trading operations are in paper trading mode only", "category": "trading", "status": "PASSED", "duration": 0.5592377185821533, "error": null, "results": {"paper_mode_enforced": true, "analysis_generated": true, "live_trading_blocked": true, "trading_engines_tested": 2}, "user_feedback": "✅ Paper trading mode properly enforced"}, {"name": "AI Conversation Flow", "description": "Test AI conversation capabilities and response quality", "category": "ai", "status": "PASSED", "duration": 5.475430011749268, "error": null, "results": {"total_messages_tested": 5, "successful_responses": 5, "success_rate": 100.0, "responses": [{"message": "Hello, I'm new to trading", "response_received": true, "response_length": 363}, {"message": "What is the current price of AAPL?", "response_received": true, "response_length": 589}, {"message": "Should I buy TSLA stock?", "response_received": true, "response_length": 401}, {"message": "Explain the Lee Method", "response_received": true, "response_length": 913}, {"message": "What are the risks of options trading?", "response_received": true, "response_length": 913}]}, "user_feedback": "✅ AI conversation working (5/5 responses)"}, {"name": "Enhanced Market Scanner Functionality", "description": "Test Lee Method scanner with enhanced rate limiting and fallback data sources", "category": "scanner", "status": "FAILED", "duration": 20.540706396102905, "error": null, "results": {"scanner_initialized": true, "data_manager_working": true, "rate_limiter_working": true, "symbols_tested": 5, "successful_scans": 0, "success_rate": 0.0, "average_scan_time": 3.029950714111328, "signals_found": 0, "batch_quote_success": 0, "batch_quote_rate": 0.0, "batch_scan_success": true, "batch_scan_time": 4.365593194961548, "scan_results": [{"symbol": "AAPL", "scan_successful": false, "scan_time": 3.0825116634368896, "has_signal": false, "signal_type": null}, {"symbol": "MSFT", "scan_successful": false, "scan_time": 3.0160462856292725, "has_signal": false, "signal_type": null}, {"symbol": "GOOGL", "scan_successful": false, "scan_time": 3.015122175216675, "has_signal": false, "signal_type": null}, {"symbol": "TSLA", "scan_successful": false, "scan_time": 3.017096757888794, "has_signal": false, "signal_type": null}, {"symbol": "NVDA", "scan_successful": false, "scan_time": 3.0189766883850098, "has_signal": false, "signal_type": null}]}, "user_feedback": "❌ Scanner needs improvement (0/5 scans, 0.0% success rate, avg 3.0s)"}, {"name": "Risk Management Validation", "description": "Test risk assessment and position sizing functionality", "category": "risk", "status": "PASSED", "duration": 0.0012254714965820312, "error": null, "results": {"positions_tested": 3, "successful_assessments": 3, "assessments": [{"symbol": "AAPL", "assessment_successful": true, "risk_score": 8.0, "position_size": 15000.0}, {"symbol": "MSFT", "assessment_successful": true, "risk_score": 8.0, "position_size": 15000.0}, {"symbol": "GOOGL", "assessment_successful": true, "risk_score": 8.0, "position_size": 62500.0}]}, "user_feedback": "✅ Risk management working (3/3 assessments)"}, {"name": "System Performance", "description": "Test system response times and performance under load", "category": "performance", "status": "PASSED", "duration": 0.00047016143798828125, "error": null, "results": {"total_performance_tests": 3, "acceptable_performance": 3, "performance_tests": [{"test": "AI Engine Initialization", "duration": 0.0001773834228515625, "acceptable": true, "threshold": "< 10 seconds"}, {"test": "Trading Engine Initialization", "duration": 5.793571472167969e-05, "acceptable": true, "threshold": "< 5 seconds"}, {"test": "Scanner Initialization", "duration": 0.0002269744873046875, "acceptable": true, "threshold": "< 3 seconds"}]}, "user_feedback": "✅ Performance acceptable (3/3 tests)"}], "recommendations": ["❌ 1 UAT scenarios failed - address before production", "🔍 Review failed scenario details and fix issues", "🔄 Re-run UAT after fixes", "⚠️ Good user experience - minor issues to address"]}