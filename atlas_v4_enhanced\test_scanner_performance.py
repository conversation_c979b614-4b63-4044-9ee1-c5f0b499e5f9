"""
A.T.L.A.S. Scanner Performance Test
Dedicated test for validating scanner rate limiting fixes and performance
"""

import asyncio
import logging
import time
import json
import sys
from datetime import datetime
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ScannerPerformanceTest:
    """Comprehensive scanner performance and rate limiting test"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
    async def test_rate_limiter_functionality(self) -> Dict[str, Any]:
        """Test rate limiter basic functionality"""
        logger.info("Testing rate limiter functionality...")
        
        try:
            from atlas_rate_limiter import rate_limiter, APIProvider
            
            # Start rate limiter
            await rate_limiter.start_processing()
            
            # Test basic request
            result = await rate_limiter.make_request(
                APIProvider.FMP,
                "quote/AAPL",
                {"apikey": "demo"},
                priority=1
            )
            
            # Get metrics
            metrics = rate_limiter.get_metrics()
            
            return {
                "rate_limiter_available": True,
                "request_successful": result is not None,
                "metrics": metrics,
                "status": "PASSED"
            }
            
        except Exception as e:
            logger.error(f"Rate limiter test failed: {e}")
            return {
                "rate_limiter_available": False,
                "error": str(e),
                "status": "FAILED"
            }
    
    async def test_enhanced_market_data(self) -> Dict[str, Any]:
        """Test enhanced market data manager"""
        logger.info("Testing enhanced market data manager...")
        
        try:
            from atlas_enhanced_market_data import enhanced_market_data
            
            # Test single quote
            start_time = time.time()
            quote = await enhanced_market_data.get_quote("AAPL", timeout=10.0)
            single_quote_time = time.time() - start_time
            
            # Test batch quotes
            start_time = time.time()
            batch_quotes = await enhanced_market_data.get_multiple_quotes(
                ["AAPL", "MSFT", "GOOGL"], timeout=15.0
            )
            batch_quote_time = time.time() - start_time
            
            # Test historical data
            start_time = time.time()
            historical = await enhanced_market_data.get_historical_data("AAPL")
            historical_time = time.time() - start_time
            
            # Get metrics
            metrics = enhanced_market_data.get_metrics()
            
            successful_batch_quotes = len([q for q in batch_quotes.values() if q is not None])
            
            return {
                "single_quote_success": quote is not None,
                "single_quote_time": single_quote_time,
                "batch_quotes_success": successful_batch_quotes,
                "batch_quotes_total": len(batch_quotes),
                "batch_quote_time": batch_quote_time,
                "historical_data_success": historical is not None,
                "historical_time": historical_time,
                "metrics": metrics,
                "status": "PASSED" if quote is not None else "FAILED"
            }
            
        except Exception as e:
            logger.error(f"Enhanced market data test failed: {e}")
            return {
                "error": str(e),
                "status": "FAILED"
            }
    
    async def test_scanner_individual_symbols(self) -> Dict[str, Any]:
        """Test scanner with individual symbols"""
        logger.info("Testing scanner with individual symbols...")
        
        try:
            from atlas_lee_method import LeeMethodScanner
            
            scanner = LeeMethodScanner()
            test_symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
            
            scan_results = []
            total_start_time = time.time()
            
            for symbol in test_symbols:
                try:
                    start_time = time.time()
                    result = await scanner.scan_symbol(symbol)
                    scan_time = time.time() - start_time
                    
                    scan_results.append({
                        "symbol": symbol,
                        "success": result is not None,
                        "scan_time": scan_time,
                        "has_signal": hasattr(result, 'signal_type') if result else False,
                        "signal_type": result.signal_type if result and hasattr(result, 'signal_type') else None
                    })
                    
                    logger.info(f"Scanned {symbol}: {'✅' if result else '❌'} ({scan_time:.2f}s)")
                    
                except Exception as e:
                    scan_results.append({
                        "symbol": symbol,
                        "success": False,
                        "scan_time": 0.0,
                        "error": str(e)
                    })
                    logger.error(f"Failed to scan {symbol}: {e}")
            
            total_time = time.time() - total_start_time
            successful_scans = len([r for r in scan_results if r["success"]])
            average_time = sum(r["scan_time"] for r in scan_results) / len(scan_results) if scan_results else 0
            signals_found = len([r for r in scan_results if r.get("has_signal", False)])
            
            return {
                "symbols_tested": len(test_symbols),
                "successful_scans": successful_scans,
                "success_rate": successful_scans / len(test_symbols),
                "total_time": total_time,
                "average_scan_time": average_time,
                "signals_found": signals_found,
                "scan_results": scan_results,
                "status": "PASSED" if successful_scans >= len(test_symbols) * 0.6 else "FAILED"  # 60% success rate
            }
            
        except Exception as e:
            logger.error(f"Individual scanner test failed: {e}")
            return {
                "error": str(e),
                "status": "FAILED"
            }
    
    async def test_scanner_batch_processing(self) -> Dict[str, Any]:
        """Test scanner batch processing"""
        logger.info("Testing scanner batch processing...")
        
        try:
            from atlas_lee_method import LeeMethodScanner
            
            scanner = LeeMethodScanner()
            test_symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN", "META", "NFLX"]
            
            start_time = time.time()
            signals = await scanner.scan_multiple_symbols(test_symbols)
            batch_time = time.time() - start_time
            
            return {
                "symbols_tested": len(test_symbols),
                "signals_found": len(signals),
                "batch_time": batch_time,
                "average_time_per_symbol": batch_time / len(test_symbols),
                "signals": [
                    {
                        "symbol": signal.symbol,
                        "signal_type": signal.signal_type,
                        "confidence": signal.confidence
                    } for signal in signals
                ],
                "status": "PASSED" if len(signals) >= 0 else "FAILED"  # Any result is acceptable
            }
            
        except Exception as e:
            logger.error(f"Batch scanner test failed: {e}")
            return {
                "error": str(e),
                "status": "FAILED"
            }
    
    async def test_scanner_performance_under_load(self) -> Dict[str, Any]:
        """Test scanner performance under load"""
        logger.info("Testing scanner performance under load...")
        
        try:
            from atlas_lee_method import LeeMethodScanner
            from sp500_symbols import get_core_sp500_symbols
            
            scanner = LeeMethodScanner()
            
            # Get a subset of S&P 500 symbols for load testing
            all_symbols = get_core_sp500_symbols()
            test_symbols = all_symbols[:20]  # Test with 20 symbols
            
            start_time = time.time()
            signals = await scanner.scan_multiple_symbols(test_symbols)
            load_test_time = time.time() - start_time
            
            return {
                "symbols_tested": len(test_symbols),
                "signals_found": len(signals),
                "load_test_time": load_test_time,
                "average_time_per_symbol": load_test_time / len(test_symbols),
                "throughput_symbols_per_minute": (len(test_symbols) / load_test_time) * 60,
                "status": "PASSED" if load_test_time < 300 else "FAILED"  # Should complete within 5 minutes
            }
            
        except Exception as e:
            logger.error(f"Load test failed: {e}")
            return {
                "error": str(e),
                "status": "FAILED"
            }
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive scanner performance test"""
        logger.info("="*80)
        logger.info("A.T.L.A.S. SCANNER PERFORMANCE TEST STARTED")
        logger.info("="*80)
        
        # Run all tests
        test_methods = [
            ("Rate Limiter Functionality", self.test_rate_limiter_functionality),
            ("Enhanced Market Data", self.test_enhanced_market_data),
            ("Individual Symbol Scanning", self.test_scanner_individual_symbols),
            ("Batch Processing", self.test_scanner_batch_processing),
            ("Performance Under Load", self.test_scanner_performance_under_load)
        ]
        
        results = {}
        
        for test_name, test_method in test_methods:
            logger.info(f"\n--- Running {test_name} ---")
            try:
                result = await test_method()
                results[test_name] = result
                status = result.get("status", "UNKNOWN")
                logger.info(f"{test_name}: {status}")
            except Exception as e:
                logger.error(f"{test_name} failed: {e}")
                results[test_name] = {"error": str(e), "status": "FAILED"}
        
        # Calculate overall metrics
        total_tests = len(results)
        passed_tests = len([r for r in results.values() if r.get("status") == "PASSED"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        summary = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate,
                "total_duration": total_duration,
                "status": "PASSED" if failed_tests == 0 else "FAILED"
            },
            "detailed_results": results,
            "recommendations": self._generate_recommendations(success_rate, failed_tests, results)
        }
        
        # Save test report
        with open("scanner_performance_test_report.json", "w") as f:
            json.dump(summary, f, indent=2, default=str)
        
        return summary
    
    def _generate_recommendations(self, success_rate: float, failed_tests: int, results: Dict) -> List[str]:
        """Generate test recommendations"""
        recommendations = []
        
        if failed_tests == 0:
            recommendations.append("✅ All scanner tests passed! Rate limiting fixes successful")
            recommendations.append("🚀 Scanner ready for production deployment")
            recommendations.append("📈 Performance meets requirements for real-time scanning")
        else:
            recommendations.append(f"❌ {failed_tests} scanner tests failed")
            recommendations.append("🔍 Review failed test details and address issues")
            recommendations.append("🔄 Re-run tests after fixes")
        
        # Specific recommendations based on results
        if "Individual Symbol Scanning" in results:
            scan_result = results["Individual Symbol Scanning"]
            if scan_result.get("success_rate", 0) < 0.8:
                recommendations.append("⚠️ Individual scanning success rate below 80% - check data sources")
        
        if "Enhanced Market Data" in results:
            data_result = results["Enhanced Market Data"]
            if not data_result.get("single_quote_success", False):
                recommendations.append("🚨 Market data fetching issues - verify API keys and connectivity")
        
        if success_rate >= 80:
            recommendations.append("🎯 Excellent scanner performance - rate limiting fixes effective")
        elif success_rate >= 60:
            recommendations.append("⚠️ Good scanner performance - minor optimizations needed")
        else:
            recommendations.append("🚨 Poor scanner performance - significant issues need attention")
        
        return recommendations


async def main():
    """Main test execution"""
    test = ScannerPerformanceTest()
    results = await test.run_comprehensive_test()
    
    # Print summary
    print("\n" + "="*80)
    print("A.T.L.A.S. SCANNER PERFORMANCE TEST RESULTS")
    print("="*80)
    print(f"Total Tests: {results['test_summary']['total_tests']}")
    print(f"Passed: {results['test_summary']['passed_tests']}")
    print(f"Failed: {results['test_summary']['failed_tests']}")
    print(f"Success Rate: {results['test_summary']['success_rate']:.1f}%")
    print(f"Duration: {results['test_summary']['total_duration']:.2f} seconds")
    print(f"Status: {results['test_summary']['status']}")
    
    print(f"\nDetailed Results:")
    for test_name, result in results['detailed_results'].items():
        status_icon = "✅" if result.get('status') == "PASSED" else "❌"
        print(f"  {status_icon} {test_name}: {result.get('status', 'UNKNOWN')}")
        if result.get('status') == "FAILED" and 'error' in result:
            print(f"    Error: {result['error']}")
    
    print(f"\nRecommendations:")
    for rec in results['recommendations']:
        print(f"  {rec}")
    
    print("="*80)
    
    return results['test_summary']['status'] == "PASSED"


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
