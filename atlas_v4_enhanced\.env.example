# A.T.L.A.S Trading System Environment Variables - EXAMPLE FILE
# Copy this to .env and replace placeholder values with your actual API keys

# =============================================================================
# SECURITY WARNING: Never commit .env files with real API keys to version control
# =============================================================================

# CRITICAL SECURITY: Trading Mode Configuration
# ALWAYS USE PAPER TRADING FOR TESTING - NEVER SET TO LIVE WITHOUT PROPER AUTHORIZATION
ATLAS_TRADING_MODE=PAPER
PAPER_TRADING=true

# Alpaca Trading API
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here

# Financial Modeling Prep API
FMP_API_KEY=your_fmp_api_key_here

# Grok AI (X.AI) API - Primary AI Provider
GROK_API_KEY=your_grok_api_key_here

# OpenAI API - Fallback AI Provider
OPENAI_API_KEY=your_openai_api_key_here

# Predicto API (Optional)
PREDICTO_API_KEY=your_predicto_api_key_here

# Trading Configuration
PAPER_TRADING=true
DEBUG=true
ENVIRONMENT=development

# Validation Mode (set to true for testing without all API keys)
VALIDATION_MODE=false

# Enhanced Security Settings
LOG_LEVEL=INFO
PORT=8080

# Database Configuration
DATABASE_URL=sqlite:///atlas.db

# Performance Settings
API_TIMEOUT=30
CACHE_TTL=300
MAX_SCAN_RESULTS=50

# ML Model Configuration
ML_MODELS_ENABLED=true
ML_PREDICTION_CONFIDENCE_THRESHOLD=0.7

# Options Trading Configuration
OPTIONS_TRADING_ENABLED=true
OPTIONS_MAX_EXPIRY_DAYS=45
OPTIONS_MIN_VOLUME=100
OPTIONS_MAX_SPREAD_PERCENT=5.0

# Risk Management
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10

# Proactive Assistant Configuration
PROACTIVE_ASSISTANT_ENABLED=true
MORNING_BRIEFING_TIME=09:00
ALERT_COOLDOWN_MINUTES=15
MIN_SIGNAL_STRENGTH=4

# Enhanced Memory System
ENHANCED_MEMORY_ENABLED=true
CONVERSATION_MEMORY_LIMIT=1000
MEMORY_IMPORTANCE_THRESHOLD=0.5

# Optional APIs (for enhanced functionality)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here

# Social Media APIs (for sentiment analysis)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
